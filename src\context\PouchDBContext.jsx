/**
 * PouchDB Context Provider
 * Provides React context for PouchDB operations with hooks and state management
 */

import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import PouchDBService from '../utils/PouchDBService';

// Create PouchDB Context
const PouchDBContext = createContext();

// Custom hook to use PouchDB context
export const usePouchDB = () => {
  const context = useContext(PouchDBContext);
  if (!context) {
    throw new Error('usePouchDB must be used within a PouchDBProvider');
  }
  return context;
};

// PouchDB Provider component
export const PouchDBProvider = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState('idle'); // idle, syncing, error, complete
  const [error, setError] = useState(null);

  // Initialize PouchDB service
  useEffect(() => {
    const initializeService = async () => {
      try {
        await PouchDBService.waitForInitialization();
        setIsInitialized(true);
        setError(null);
      } catch (err) {
        console.error('Failed to initialize PouchDB service:', err);
        setError(err.message);
      }
    };

    initializeService();

    // Listen for PouchDB events
    const handleChange = (data) => {
      // Handle database changes
      console.log('Database change:', data);
    };

    const handleInitialized = () => {
      setIsInitialized(true);
      setError(null);
    };

    PouchDBService.on('change', handleChange);
    PouchDBService.on('initialized', handleInitialized);

    return () => {
      PouchDBService.off('change', handleChange);
      PouchDBService.off('initialized', handleInitialized);
    };
  }, []);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Roadmap operations
  const saveRoadmap = useCallback(async (roadmapData, originalData = null) => {
    try {
      const result = await PouchDBService.saveRoadmap(roadmapData, originalData);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.roadmapId;
    } catch (err) {
      console.error('Error saving roadmap:', err);
      throw err;
    }
  }, []);

  const loadRoadmap = useCallback(async (roadmapId) => {
    try {
      const result = await PouchDBService.loadRoadmap(roadmapId);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    } catch (err) {
      console.error('Error loading roadmap:', err);
      throw err;
    }
  }, []);

  const updateRoadmapData = useCallback(async (roadmapId, updatedData) => {
    try {
      const result = await PouchDBService.updateRoadmapData(roadmapId, updatedData);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error updating roadmap:', err);
      throw err;
    }
  }, []);

  const deleteRoadmap = useCallback(async (roadmapId) => {
    try {
      const result = await PouchDBService.deleteRoadmap(roadmapId);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error deleting roadmap:', err);
      throw err;
    }
  }, []);

  const getAllRoadmapMetadata = useCallback(async () => {
    try {
      const result = await PouchDBService.getAllRoadmapMetadata();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.metadata;
    } catch (err) {
      console.error('Error getting roadmap metadata:', err);
      throw err;
    }
  }, []);

  // Current roadmap operations
  const getCurrentRoadmap = useCallback(async () => {
    try {
      const result = await PouchDBService.getCurrentRoadmap();
      return result.success ? result.roadmapId : null;
    } catch (err) {
      console.error('Error getting current roadmap:', err);
      return null;
    }
  }, []);

  const setCurrentRoadmap = useCallback(async (roadmapId) => {
    try {
      const result = await PouchDBService.setCurrentRoadmap(roadmapId);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error setting current roadmap:', err);
      throw err;
    }
  }, []);

  // Task completion operations
  const saveTaskCompletion = useCallback(async (roadmapId, completedTasks) => {
    try {
      const result = await PouchDBService.saveTaskCompletion(roadmapId, completedTasks);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error saving task completion:', err);
      throw err;
    }
  }, []);

  const loadTaskCompletion = useCallback(async (roadmapId) => {
    try {
      const result = await PouchDBService.loadTaskCompletion(roadmapId);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.completedTasks;
    } catch (err) {
      console.error('Error loading task completion:', err);
      return {};
    }
  }, []);

  // Subtask completion operations
  const saveSubtaskCompletion = useCallback(async (roadmapId, phaseNumber, taskId, completedSubtasks) => {
    try {
      const result = await PouchDBService.saveSubtaskCompletion(roadmapId, phaseNumber, taskId, completedSubtasks);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error saving subtask completion:', err);
      throw err;
    }
  }, []);

  const loadSubtaskCompletion = useCallback(async (roadmapId, phaseNumber, taskId) => {
    try {
      const result = await PouchDBService.loadSubtaskCompletion(roadmapId, phaseNumber, taskId);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.completedSubtasks;
    } catch (err) {
      console.error('Error loading subtask completion:', err);
      return {};
    }
  }, []);

  // Task notes operations
  const saveTaskNotes = useCallback(async (roadmapId, phaseNumber, taskId, notes) => {
    try {
      const result = await PouchDBService.saveTaskNotes(roadmapId, phaseNumber, taskId, notes);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error saving task notes:', err);
      throw err;
    }
  }, []);

  const loadTaskNotes = useCallback(async (roadmapId, phaseNumber, taskId) => {
    try {
      const result = await PouchDBService.loadTaskNotes(roadmapId, phaseNumber, taskId);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.notes;
    } catch (err) {
      console.error('Error loading task notes:', err);
      return '';
    }
  }, []);

  // Theme configuration operations
  const saveThemeConfig = useCallback(async (themePreference) => {
    try {
      const result = await PouchDBService.saveThemeConfig(themePreference);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error saving theme config:', err);
      throw err;
    }
  }, []);

  const loadThemeConfig = useCallback(async () => {
    try {
      const result = await PouchDBService.loadThemeConfig();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.themePreference;
    } catch (err) {
      console.error('Error loading theme config:', err);
      return 'system';
    }
  }, []);

  // App configuration operations
  const saveAppConfig = useCallback(async (config) => {
    try {
      const result = await PouchDBService.saveAppConfig(config);
      if (!result.success) {
        throw new Error(result.error);
      }
      return true;
    } catch (err) {
      console.error('Error saving app config:', err);
      throw err;
    }
  }, []);

  const loadAppConfig = useCallback(async () => {
    try {
      const result = await PouchDBService.loadAppConfig();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.config;
    } catch (err) {
      console.error('Error loading app config:', err);
      return null;
    }
  }, []);

  // Context value
  const value = {
    // Status
    isInitialized,
    isOnline,
    syncStatus,
    error,

    // Roadmap operations
    saveRoadmap,
    loadRoadmap,
    updateRoadmapData,
    deleteRoadmap,
    getAllRoadmapMetadata,
    getCurrentRoadmap,
    setCurrentRoadmap,

    // Task operations
    saveTaskCompletion,
    loadTaskCompletion,
    saveSubtaskCompletion,
    loadSubtaskCompletion,
    saveTaskNotes,
    loadTaskNotes,

    // Configuration operations
    saveThemeConfig,
    loadThemeConfig,
    saveAppConfig,
    loadAppConfig,
  };

  return (
    <PouchDBContext.Provider value={value}>
      {children}
    </PouchDBContext.Provider>
  );
};

export default PouchDBContext;
