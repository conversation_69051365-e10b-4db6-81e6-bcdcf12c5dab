import { useState } from "react";
import "./App.css";
import AppRouter from "./router/AppRouter";
import { ThemeProvider } from "./context/ThemeContext";
import { TooltipProvider } from "./context/TooltipContext";
import { PouchDBProvider } from "./context/PouchDBContext";
import GlobalTooltip from "./components/tooltips/GlobalTooltip";
import MigrationModal from "./components/migration/MigrationModal";

function App() {
  const [migrationComplete, setMigrationComplete] = useState(false);

  const handleMigrationComplete = () => {
    setMigrationComplete(true);
  };

  return (
    <PouchDBProvider>
      <ThemeProvider>
        <TooltipProvider>
          <AppRouter />
          <GlobalTooltip />
          {!migrationComplete && (
            <MigrationModal onComplete={handleMigrationComplete} />
          )}
        </TooltipProvider>
      </ThemeProvider>
    </PouchDBProvider>
  );
}

export default App;
