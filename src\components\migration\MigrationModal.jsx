/**
 * Migration Modal Component
 * Handles the migration from localStorage to PouchDB with user interface
 */

import { useState, useEffect } from 'react';
import PouchDBMigration, { MIGRATION_STATUS } from '../../utils/PouchDBMigration';

const MigrationModal = ({ onComplete }) => {
  const [migrationStatus, setMigrationStatus] = useState(MIGRATION_STATUS.NOT_STARTED);
  const [migrationResults, setMigrationResults] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasLocalStorageData, setHasLocalStorageData] = useState(false);

  useEffect(() => {
    // Check if migration is needed
    const checkMigrationNeeded = () => {
      const needsMigration = PouchDBMigration.isMigrationNeeded();
      const hasData = PouchDBMigration.hasLocalStorageData();
      
      setHasLocalStorageData(hasData);
      setMigrationStatus(PouchDBMigration.getMigrationStatus());
      
      // Show modal if migration is needed and there's data to migrate
      if (needsMigration && hasData) {
        setIsVisible(true);
      } else if (needsMigration && !hasData) {
        // No data to migrate, mark as completed
        PouchDBMigration.setMigrationStatus(MIGRATION_STATUS.COMPLETED);
        onComplete?.();
      } else {
        // Migration already completed
        onComplete?.();
      }
    };

    checkMigrationNeeded();
  }, [onComplete]);

  const handleStartMigration = async () => {
    try {
      setMigrationStatus(MIGRATION_STATUS.IN_PROGRESS);
      
      const results = await PouchDBMigration.performMigration();
      
      setMigrationResults(results);
      setMigrationStatus(results.success ? MIGRATION_STATUS.COMPLETED : MIGRATION_STATUS.ERROR);
      
      if (results.success) {
        setTimeout(() => {
          setIsVisible(false);
          onComplete?.();
        }, 2000);
      }
    } catch (error) {
      console.error('Migration failed:', error);
      setMigrationStatus(MIGRATION_STATUS.ERROR);
      setMigrationResults({
        success: false,
        error: error.message
      });
    }
  };

  const handleSkipMigration = () => {
    // Mark migration as completed to not show this again
    PouchDBMigration.setMigrationStatus(MIGRATION_STATUS.COMPLETED);
    setIsVisible(false);
    onComplete?.();
  };

  const handleRetryMigration = () => {
    setMigrationStatus(MIGRATION_STATUS.NOT_STARTED);
    setMigrationResults(null);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Database Upgrade
            </h2>
          </div>

          {/* Content based on migration status */}
          {migrationStatus === MIGRATION_STATUS.NOT_STARTED && (
            <div>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                We've upgraded to a more powerful database system (PouchDB) that provides better performance, 
                offline capabilities, and data synchronization.
              </p>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We found existing data in your browser. Would you like to migrate it to the new system?
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={handleStartMigration}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Migrate Data
                </button>
                <button
                  onClick={handleSkipMigration}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors"
                >
                  Start Fresh
                </button>
              </div>
            </div>
          )}

          {migrationStatus === MIGRATION_STATUS.IN_PROGRESS && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">
                Migrating your data to the new database...
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                This may take a few moments depending on the amount of data.
              </p>
            </div>
          )}

          {migrationStatus === MIGRATION_STATUS.COMPLETED && migrationResults?.success && (
            <div className="text-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Migration Successful!
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Your data has been successfully migrated to the new database system.
              </p>
              {migrationResults.results && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>Roadmaps migrated: {migrationResults.results.roadmaps.migratedCount}</p>
                  <p>Theme settings: {migrationResults.results.theme ? '✓' : '✗'}</p>
                  <p>App configuration: {migrationResults.results.appConfig ? '✓' : '✗'}</p>
                </div>
              )}
            </div>
          )}

          {migrationStatus === MIGRATION_STATUS.ERROR && (
            <div className="text-center">
              <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Migration Failed
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                There was an error migrating your data. You can try again or start fresh.
              </p>
              {migrationResults?.error && (
                <p className="text-sm text-red-600 dark:text-red-400 mb-4">
                  Error: {migrationResults.error}
                </p>
              )}
              {migrationResults?.results?.roadmaps?.errors && migrationResults.results.roadmaps.errors.length > 0 && (
                <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <p className="font-medium">Migration errors:</p>
                  <ul className="text-left list-disc list-inside">
                    {migrationResults.results.roadmaps.errors.slice(0, 3).map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                    {migrationResults.results.roadmaps.errors.length > 3 && (
                      <li>... and {migrationResults.results.roadmaps.errors.length - 3} more</li>
                    )}
                  </ul>
                </div>
              )}
              <div className="flex space-x-3">
                <button
                  onClick={handleRetryMigration}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={handleSkipMigration}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors"
                >
                  Start Fresh
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MigrationModal;
