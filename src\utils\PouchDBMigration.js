/**
 * PouchDB Migration Utility
 * Handles migration from localStorage to PouchDB with data preservation
 */

import PouchDBService from './PouchDBService';
import RoadmapPersistence from './RoadmapPersistence';

/**
 * Migration status constants
 */
const MIGRATION_STATUS = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  ERROR: 'error'
};

/**
 * Migration utility class
 */
class PouchDBMigration {
  constructor() {
    this.migrationKey = 'pouchdb-migration-status';
  }

  /**
   * Check if migration is needed
   */
  isMigrationNeeded() {
    const status = localStorage.getItem(this.migrationKey);
    return status !== MIGRATION_STATUS.COMPLETED;
  }

  /**
   * Get migration status
   */
  getMigrationStatus() {
    return localStorage.getItem(this.migrationKey) || MIGRATION_STATUS.NOT_STARTED;
  }

  /**
   * Set migration status
   */
  setMigrationStatus(status) {
    localStorage.setItem(this.migrationKey, status);
  }

  /**
   * Check if localStorage has data to migrate
   */
  hasLocalStorageData() {
    try {
      // Check for roadmap metadata
      const metadata = RoadmapPersistence.getAllRoadmapMetadata();
      if (metadata && metadata.length > 0) {
        return true;
      }

      // Check for theme preference
      const themePreference = localStorage.getItem('theme-preference');
      if (themePreference) {
        return true;
      }

      // Check for app config
      const appConfig = localStorage.getItem('roadmap-visualizer-config');
      if (appConfig) {
        return true;
      }

      return false;
    } catch (error) {
      console.warn('Error checking localStorage data:', error);
      return false;
    }
  }

  /**
   * Migrate roadmap data from localStorage to PouchDB
   */
  async migrateRoadmapData() {
    try {
      console.log('Starting roadmap data migration...');
      
      // Get all roadmap metadata from localStorage
      const allMetadata = RoadmapPersistence.getAllRoadmapMetadata();
      
      if (!allMetadata || allMetadata.length === 0) {
        console.log('No roadmap data to migrate');
        return { success: true, migratedCount: 0 };
      }

      let migratedCount = 0;
      const errors = [];

      for (const metadata of allMetadata) {
        try {
          // Load roadmap data from localStorage
          const roadmapInfo = RoadmapPersistence.loadRoadmap(metadata.id);
          
          if (roadmapInfo && roadmapInfo.data) {
            // Save to PouchDB
            const result = await PouchDBService.saveRoadmap(
              roadmapInfo.data,
              roadmapInfo.originalData
            );

            if (result.success) {
              // Migrate task completion data
              await this.migrateTaskCompletion(metadata.id, result.roadmapId);
              
              // Migrate task notes and subtasks
              await this.migrateTaskNotes(metadata.id, result.roadmapId);
              await this.migrateSubtasks(metadata.id, result.roadmapId);
              
              migratedCount++;
              console.log(`Migrated roadmap: ${metadata.title}`);
            } else {
              errors.push(`Failed to migrate roadmap ${metadata.title}: ${result.error}`);
            }
          }
        } catch (error) {
          errors.push(`Error migrating roadmap ${metadata.title}: ${error.message}`);
        }
      }

      // Migrate current roadmap setting
      const currentRoadmapId = RoadmapPersistence.getCurrentRoadmap();
      if (currentRoadmapId) {
        // Note: We can't directly map old IDs to new ones, so we'll skip this for now
        console.log('Current roadmap setting will need to be reset');
      }

      return {
        success: errors.length === 0,
        migratedCount,
        errors
      };
    } catch (error) {
      console.error('Error during roadmap migration:', error);
      return {
        success: false,
        migratedCount: 0,
        errors: [error.message]
      };
    }
  }

  /**
   * Migrate task completion data
   */
  async migrateTaskCompletion(oldRoadmapId, newRoadmapId) {
    try {
      const completionKey = `completed-tasks-${oldRoadmapId}`;
      const completedTasks = localStorage.getItem(completionKey);
      
      if (completedTasks) {
        const parsedTasks = JSON.parse(completedTasks);
        await PouchDBService.saveTaskCompletion(newRoadmapId, parsedTasks);
        console.log(`Migrated task completion for roadmap: ${oldRoadmapId}`);
      }
    } catch (error) {
      console.warn(`Error migrating task completion for ${oldRoadmapId}:`, error);
    }
  }

  /**
   * Migrate task notes
   */
  async migrateTaskNotes(oldRoadmapId, newRoadmapId) {
    try {
      // Look for task notes in localStorage
      const keys = Object.keys(localStorage);
      const noteKeys = keys.filter(key => key.startsWith('task-notes-'));
      
      for (const key of noteKeys) {
        try {
          const notes = localStorage.getItem(key);
          if (notes) {
            // Extract phase and task info from key
            const parts = key.split('-');
            if (parts.length >= 4) {
              const phaseNumber = parts[2];
              const taskId = parts[3];
              
              await PouchDBService.saveTaskNotes(newRoadmapId, phaseNumber, taskId, notes);
            }
          }
        } catch (error) {
          console.warn(`Error migrating task note ${key}:`, error);
        }
      }
    } catch (error) {
      console.warn(`Error migrating task notes for ${oldRoadmapId}:`, error);
    }
  }

  /**
   * Migrate subtask completion data
   */
  async migrateSubtasks(oldRoadmapId, newRoadmapId) {
    try {
      // Look for subtask completion in localStorage
      const keys = Object.keys(localStorage);
      const subtaskKeys = keys.filter(key => key.startsWith('subtasks-'));
      
      for (const key of subtaskKeys) {
        try {
          const subtasks = localStorage.getItem(key);
          if (subtasks) {
            const parsedSubtasks = JSON.parse(subtasks);
            
            // Extract phase and task info from key
            const parts = key.split('-');
            if (parts.length >= 3) {
              const phaseNumber = parts[1];
              const taskId = parts[2];
              
              await PouchDBService.saveSubtaskCompletion(
                newRoadmapId,
                phaseNumber,
                taskId,
                parsedSubtasks
              );
            }
          }
        } catch (error) {
          console.warn(`Error migrating subtask ${key}:`, error);
        }
      }
    } catch (error) {
      console.warn(`Error migrating subtasks for ${oldRoadmapId}:`, error);
    }
  }

  /**
   * Migrate theme configuration
   */
  async migrateThemeConfig() {
    try {
      const themePreference = localStorage.getItem('theme-preference');
      if (themePreference) {
        await PouchDBService.saveThemeConfig(themePreference);
        console.log('Migrated theme configuration');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error migrating theme config:', error);
      return false;
    }
  }

  /**
   * Migrate app configuration
   */
  async migrateAppConfig() {
    try {
      const appConfig = localStorage.getItem('roadmap-visualizer-config');
      if (appConfig) {
        const parsedConfig = JSON.parse(appConfig);
        await PouchDBService.saveAppConfig(parsedConfig);
        console.log('Migrated app configuration');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error migrating app config:', error);
      return false;
    }
  }

  /**
   * Perform complete migration
   */
  async performMigration() {
    try {
      console.log('Starting PouchDB migration...');
      this.setMigrationStatus(MIGRATION_STATUS.IN_PROGRESS);

      // Wait for PouchDB to be initialized
      await PouchDBService.waitForInitialization();

      const results = {
        roadmaps: { success: false, migratedCount: 0, errors: [] },
        theme: false,
        appConfig: false
      };

      // Migrate roadmap data
      results.roadmaps = await this.migrateRoadmapData();

      // Migrate theme configuration
      results.theme = await this.migrateThemeConfig();

      // Migrate app configuration
      results.appConfig = await this.migrateAppConfig();

      // Check overall success
      const overallSuccess = results.roadmaps.success && 
                           (results.theme || !localStorage.getItem('theme-preference')) &&
                           (results.appConfig || !localStorage.getItem('roadmap-visualizer-config'));

      if (overallSuccess) {
        this.setMigrationStatus(MIGRATION_STATUS.COMPLETED);
        console.log('Migration completed successfully');
      } else {
        this.setMigrationStatus(MIGRATION_STATUS.ERROR);
        console.error('Migration completed with errors');
      }

      return {
        success: overallSuccess,
        results
      };
    } catch (error) {
      console.error('Migration failed:', error);
      this.setMigrationStatus(MIGRATION_STATUS.ERROR);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Reset migration status (for testing or re-migration)
   */
  resetMigrationStatus() {
    localStorage.removeItem(this.migrationKey);
  }
}

// Export singleton instance
export default new PouchDBMigration();
export { MIGRATION_STATUS };
