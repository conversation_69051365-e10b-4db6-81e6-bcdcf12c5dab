/**
 * PouchDB Service Layer
 * Professional implementation for offline-first data storage with sync capabilities
 */

import PouchDB from "pouchdb-browser";
import PouchDBFind from "pouchdb-find";
import PouchDBUpsert from "pouchdb-upsert";

// Add plugins
PouchDB.plugin(PouchDBFind);
PouchDB.plugin(PouchDBUpsert);

/**
 * Database configuration and constants
 */
const DB_CONFIG = {
  // Database names
  ROADMAPS: "roadmap-visualizer-roadmaps",
  METADATA: "roadmap-visualizer-metadata",
  TASKS: "roadmap-visualizer-tasks",
  CONFIG: "roadmap-visualizer-config",
  SYNC_STATE: "roadmap-visualizer-sync",

  // Database options
  OPTIONS: {
    auto_compaction: true,
    revs_limit: 10,
    adapter: "idb", // Use IndexedDB adapter
  },

  // Document types
  DOC_TYPES: {
    ROADMAP: "roadmap",
    METADATA: "metadata",
    TASK_COMPLETION: "task_completion",
    SUBTASK_COMPLETION: "subtask_completion",
    TASK_NOTES: "task_notes",
    THEME_CONFIG: "theme_config",
    APP_CONFIG: "app_config",
    SYNC_STATE: "sync_state",
  },
};

/**
 * PouchDB Service Class
 * Provides a clean, professional interface for all database operations
 */
class PouchDBService {
  constructor() {
    this.databases = {};
    this.syncHandlers = {};
    this.isInitialized = false;
    this.eventListeners = new Map();

    // Initialize databases
    this.initializeDatabases();
  }

  /**
   * Initialize all required databases
   */
  async initializeDatabases() {
    try {
      // Create database instances
      this.databases.roadmaps = new PouchDB(
        DB_CONFIG.ROADMAPS,
        DB_CONFIG.OPTIONS
      );
      this.databases.metadata = new PouchDB(
        DB_CONFIG.METADATA,
        DB_CONFIG.OPTIONS
      );
      this.databases.tasks = new PouchDB(DB_CONFIG.TASKS, DB_CONFIG.OPTIONS);
      this.databases.config = new PouchDB(DB_CONFIG.CONFIG, DB_CONFIG.OPTIONS);
      this.databases.syncState = new PouchDB(
        DB_CONFIG.SYNC_STATE,
        DB_CONFIG.OPTIONS
      );

      // Create indexes for better query performance
      await this.createIndexes();

      // Set up change listeners
      this.setupChangeListeners();

      this.isInitialized = true;
      this.emit("initialized");

      console.log("PouchDB Service initialized successfully");
    } catch (error) {
      console.error("Failed to initialize PouchDB Service:", error);
      throw error;
    }
  }

  /**
   * Create database indexes for optimized queries
   */
  async createIndexes() {
    const indexes = [
      // Roadmaps indexes
      { db: "roadmaps", index: { fields: ["type", "createdAt"] } },
      { db: "roadmaps", index: { fields: ["type", "lastAccessed"] } },
      { db: "roadmaps", index: { fields: ["type", "title"] } },

      // Metadata indexes
      { db: "metadata", index: { fields: ["type", "roadmapId"] } },
      { db: "metadata", index: { fields: ["type", "lastAccessed"] } },

      // Tasks indexes
      { db: "tasks", index: { fields: ["type", "roadmapId"] } },
      { db: "tasks", index: { fields: ["type", "roadmapId", "phaseNumber"] } },
      { db: "tasks", index: { fields: ["type", "roadmapId", "taskId"] } },

      // Config indexes
      { db: "config", index: { fields: ["type", "userId"] } },
      { db: "config", index: { fields: ["type", "configKey"] } },
    ];

    for (const { db, index } of indexes) {
      try {
        await this.databases[db].createIndex(index);
      } catch (error) {
        // Index might already exist, which is fine
        if (error.status !== 409) {
          console.warn(`Failed to create index for ${db}:`, error);
        }
      }
    }
  }

  /**
   * Set up change listeners for real-time updates
   */
  setupChangeListeners() {
    Object.keys(this.databases).forEach((dbName) => {
      this.databases[dbName]
        .changes({
          since: "now",
          live: true,
          include_docs: true,
        })
        .on("change", (change) => {
          this.emit("change", { database: dbName, change });
        })
        .on("error", (error) => {
          console.error(`Change listener error for ${dbName}:`, error);
        });
    });
  }

  /**
   * Event emitter functionality
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Generate a unique document ID
   */
  generateId(prefix = "") {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return prefix
      ? `${prefix}-${timestamp}-${random}`
      : `${timestamp}-${random}`;
  }

  /**
   * Create a standardized document structure
   */
  createDocument(type, data, id = null) {
    const now = new Date().toISOString();
    return {
      _id: id || this.generateId(type),
      type,
      ...data,
      createdAt: now,
      updatedAt: now,
      version: 1,
    };
  }

  /**
   * Update a document with proper versioning
   */
  updateDocument(existingDoc, updates) {
    return {
      ...existingDoc,
      ...updates,
      updatedAt: new Date().toISOString(),
      version: (existingDoc.version || 1) + 1,
    };
  }

  /**
   * Wait for initialization to complete
   */
  async waitForInitialization() {
    if (this.isInitialized) {
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      this.on("initialized", resolve);
    });
  }

  /**
   * Get database instance
   */
  getDatabase(name) {
    if (!this.databases[name]) {
      throw new Error(`Database ${name} not found`);
    }
    return this.databases[name];
  }

  /**
   * Generic document operations
   */
  async save(dbName, doc) {
    await this.waitForInitialization();
    const db = this.getDatabase(dbName);

    try {
      const result = await db.put(doc);
      return { success: true, id: result.id, rev: result.rev };
    } catch (error) {
      console.error(`Error saving document to ${dbName}:`, error);
      return { success: false, error: error.message };
    }
  }

  async get(dbName, id) {
    await this.waitForInitialization();
    const db = this.getDatabase(dbName);

    try {
      const doc = await db.get(id);
      return { success: true, doc };
    } catch (error) {
      if (error.status === 404) {
        return { success: false, error: "Document not found" };
      }
      console.error(`Error getting document from ${dbName}:`, error);
      return { success: false, error: error.message };
    }
  }

  async delete(dbName, id, rev) {
    await this.waitForInitialization();
    const db = this.getDatabase(dbName);

    try {
      const result = await db.remove(id, rev);
      return { success: true, id: result.id, rev: result.rev };
    } catch (error) {
      console.error(`Error deleting document from ${dbName}:`, error);
      return { success: false, error: error.message };
    }
  }

  async find(dbName, selector, options = {}) {
    await this.waitForInitialization();
    const db = this.getDatabase(dbName);

    try {
      const result = await db.find({
        selector,
        ...options,
      });
      return { success: true, docs: result.docs };
    } catch (error) {
      console.error(`Error finding documents in ${dbName}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Roadmap-specific operations
   */
  async saveRoadmap(roadmapData, originalData = null) {
    const roadmapId = this.generateId("roadmap");
    const timestamp = new Date().toISOString();

    // Create roadmap document
    const roadmapDoc = this.createDocument(DB_CONFIG.DOC_TYPES.ROADMAP, {
      roadmapId,
      data: roadmapData,
      originalData: originalData || roadmapData,
      savedAt: timestamp,
      lastAccessed: timestamp,
    });

    // Create metadata document
    const metadataDoc = this.createDocument(DB_CONFIG.DOC_TYPES.METADATA, {
      roadmapId,
      title: roadmapData.title,
      description: roadmapData.description || "",
      project_level: roadmapData.project_level || "beginner",
      tags: roadmapData.tags || [],
      uploadDate: timestamp,
      lastAccessed: timestamp,
      totalPhases: this.calculateTotalPhases(roadmapData),
      totalTasks: this.calculateTotalTasks(roadmapData),
      progressPercentage: 0,
    });

    try {
      // Save both documents
      const roadmapResult = await this.save("roadmaps", roadmapDoc);
      const metadataResult = await this.save("metadata", metadataDoc);

      if (roadmapResult.success && metadataResult.success) {
        // Set as current roadmap
        await this.setCurrentRoadmap(roadmapId);
        return { success: true, roadmapId };
      } else {
        throw new Error("Failed to save roadmap or metadata");
      }
    } catch (error) {
      console.error("Error saving roadmap:", error);
      return { success: false, error: error.message };
    }
  }

  async loadRoadmap(roadmapId) {
    try {
      // Find roadmap document
      const roadmapResult = await this.find("roadmaps", {
        type: DB_CONFIG.DOC_TYPES.ROADMAP,
        roadmapId,
      });

      if (!roadmapResult.success || roadmapResult.docs.length === 0) {
        return { success: false, error: "Roadmap not found" };
      }

      const roadmapDoc = roadmapResult.docs[0];

      // Update last accessed time
      const updatedDoc = this.updateDocument(roadmapDoc, {
        lastAccessed: new Date().toISOString(),
      });

      await this.save("roadmaps", updatedDoc);

      // Update metadata last accessed
      await this.updateLastAccessed(roadmapId);

      // Set as current roadmap
      await this.setCurrentRoadmap(roadmapId);

      return { success: true, data: roadmapDoc };
    } catch (error) {
      console.error("Error loading roadmap:", error);
      return { success: false, error: error.message };
    }
  }

  async updateRoadmapData(roadmapId, updatedData) {
    try {
      // Find and update roadmap document
      const roadmapResult = await this.find("roadmaps", {
        type: DB_CONFIG.DOC_TYPES.ROADMAP,
        roadmapId,
      });

      if (!roadmapResult.success || roadmapResult.docs.length === 0) {
        return { success: false, error: "Roadmap not found" };
      }

      const roadmapDoc = roadmapResult.docs[0];
      const updatedRoadmapDoc = this.updateDocument(roadmapDoc, {
        data: updatedData,
        lastAccessed: new Date().toISOString(),
      });

      const saveResult = await this.save("roadmaps", updatedRoadmapDoc);

      if (saveResult.success) {
        // Update metadata
        const metadata = {
          roadmapId,
          title: updatedData.title,
          description: updatedData.description || "",
          project_level: updatedData.project_level || "beginner",
          tags: updatedData.tags || [],
          lastAccessed: new Date().toISOString(),
          totalPhases: this.calculateTotalPhases(updatedData),
          totalTasks: this.calculateTotalTasks(updatedData),
        };

        await this.updateRoadmapMetadata(metadata);
        return { success: true };
      }

      return saveResult;
    } catch (error) {
      console.error("Error updating roadmap data:", error);
      return { success: false, error: error.message };
    }
  }

  async getAllRoadmapMetadata() {
    try {
      const result = await this.find(
        "metadata",
        {
          type: DB_CONFIG.DOC_TYPES.METADATA,
        },
        {
          sort: [{ lastAccessed: "desc" }],
        }
      );

      if (result.success) {
        return { success: true, metadata: result.docs };
      }
      return result;
    } catch (error) {
      console.error("Error getting roadmap metadata:", error);
      return { success: false, error: error.message };
    }
  }

  async deleteRoadmap(roadmapId) {
    try {
      // Find and delete roadmap document
      const roadmapResult = await this.find("roadmaps", {
        type: DB_CONFIG.DOC_TYPES.ROADMAP,
        roadmapId,
      });

      // Find and delete metadata document
      const metadataResult = await this.find("metadata", {
        type: DB_CONFIG.DOC_TYPES.METADATA,
        roadmapId,
      });

      // Find and delete all task-related documents
      const tasksResult = await this.find("tasks", {
        type: {
          $in: [
            DB_CONFIG.DOC_TYPES.TASK_COMPLETION,
            DB_CONFIG.DOC_TYPES.SUBTASK_COMPLETION,
            DB_CONFIG.DOC_TYPES.TASK_NOTES,
          ],
        },
        roadmapId,
      });

      const deletePromises = [];

      // Delete roadmap
      if (roadmapResult.success && roadmapResult.docs.length > 0) {
        const doc = roadmapResult.docs[0];
        deletePromises.push(this.delete("roadmaps", doc._id, doc._rev));
      }

      // Delete metadata
      if (metadataResult.success && metadataResult.docs.length > 0) {
        const doc = metadataResult.docs[0];
        deletePromises.push(this.delete("metadata", doc._id, doc._rev));
      }

      // Delete task-related documents
      if (tasksResult.success) {
        tasksResult.docs.forEach((doc) => {
          deletePromises.push(this.delete("tasks", doc._id, doc._rev));
        });
      }

      await Promise.all(deletePromises);

      // Clear current roadmap if it was the deleted one
      const currentRoadmap = await this.getCurrentRoadmap();
      if (currentRoadmap.success && currentRoadmap.roadmapId === roadmapId) {
        await this.clearCurrentRoadmap();
      }

      return { success: true };
    } catch (error) {
      console.error("Error deleting roadmap:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Helper methods for roadmap operations
   */
  calculateTotalPhases(roadmapData) {
    return roadmapData.phases ? roadmapData.phases.length : 0;
  }

  calculateTotalTasks(roadmapData) {
    if (!roadmapData.phases) return 0;
    return roadmapData.phases.reduce((total, phase) => {
      return total + (phase.tasks ? phase.tasks.length : 0);
    }, 0);
  }

  async updateRoadmapMetadata(metadata) {
    try {
      // Find existing metadata document
      const result = await this.find("metadata", {
        type: DB_CONFIG.DOC_TYPES.METADATA,
        roadmapId: metadata.roadmapId,
      });

      if (result.success && result.docs.length > 0) {
        // Update existing document
        const existingDoc = result.docs[0];
        const updatedDoc = this.updateDocument(existingDoc, metadata);
        return await this.save("metadata", updatedDoc);
      } else {
        // Create new metadata document
        const newDoc = this.createDocument(
          DB_CONFIG.DOC_TYPES.METADATA,
          metadata
        );
        return await this.save("metadata", newDoc);
      }
    } catch (error) {
      console.error("Error updating roadmap metadata:", error);
      return { success: false, error: error.message };
    }
  }

  async updateLastAccessed(roadmapId) {
    const timestamp = new Date().toISOString();
    return await this.updateRoadmapMetadata({
      roadmapId,
      lastAccessed: timestamp,
    });
  }

  async setCurrentRoadmap(roadmapId) {
    try {
      const doc = this.createDocument(
        DB_CONFIG.DOC_TYPES.APP_CONFIG,
        {
          configKey: "current_roadmap",
          roadmapId,
        },
        "current-roadmap"
      );

      return await this.save("config", doc);
    } catch (error) {
      console.error("Error setting current roadmap:", error);
      return { success: false, error: error.message };
    }
  }

  async getCurrentRoadmap() {
    try {
      const result = await this.get("config", "current-roadmap");
      if (result.success) {
        return { success: true, roadmapId: result.doc.roadmapId };
      }
      return { success: false, error: "No current roadmap set" };
    } catch (error) {
      console.error("Error getting current roadmap:", error);
      return { success: false, error: error.message };
    }
  }

  async clearCurrentRoadmap() {
    try {
      const result = await this.get("config", "current-roadmap");
      if (result.success) {
        return await this.delete("config", result.doc._id, result.doc._rev);
      }
      return { success: true }; // Already cleared
    } catch (error) {
      console.error("Error clearing current roadmap:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Task completion operations
   */
  async saveTaskCompletion(roadmapId, completedTasks) {
    try {
      const docId = `task-completion-${roadmapId}`;
      const doc = this.createDocument(
        DB_CONFIG.DOC_TYPES.TASK_COMPLETION,
        {
          roadmapId,
          completedTasks,
        },
        docId
      );

      return await this.save("tasks", doc);
    } catch (error) {
      console.error("Error saving task completion:", error);
      return { success: false, error: error.message };
    }
  }

  async loadTaskCompletion(roadmapId) {
    try {
      const docId = `task-completion-${roadmapId}`;
      const result = await this.get("tasks", docId);

      if (result.success) {
        return { success: true, completedTasks: result.doc.completedTasks };
      }
      return { success: true, completedTasks: {} }; // Return empty if not found
    } catch (error) {
      console.error("Error loading task completion:", error);
      return { success: false, error: error.message };
    }
  }

  async saveSubtaskCompletion(
    roadmapId,
    phaseNumber,
    taskId,
    completedSubtasks
  ) {
    try {
      const docId = `subtask-completion-${roadmapId}-${phaseNumber}-${taskId}`;
      const doc = this.createDocument(
        DB_CONFIG.DOC_TYPES.SUBTASK_COMPLETION,
        {
          roadmapId,
          phaseNumber,
          taskId,
          completedSubtasks,
        },
        docId
      );

      return await this.save("tasks", doc);
    } catch (error) {
      console.error("Error saving subtask completion:", error);
      return { success: false, error: error.message };
    }
  }

  async loadSubtaskCompletion(roadmapId, phaseNumber, taskId) {
    try {
      const docId = `subtask-completion-${roadmapId}-${phaseNumber}-${taskId}`;
      const result = await this.get("tasks", docId);

      if (result.success) {
        return {
          success: true,
          completedSubtasks: result.doc.completedSubtasks,
        };
      }
      return { success: true, completedSubtasks: {} }; // Return empty if not found
    } catch (error) {
      console.error("Error loading subtask completion:", error);
      return { success: false, error: error.message };
    }
  }

  async saveTaskNotes(roadmapId, phaseNumber, taskId, notes) {
    try {
      const docId = `task-notes-${roadmapId}-${phaseNumber}-${taskId}`;
      const doc = this.createDocument(
        DB_CONFIG.DOC_TYPES.TASK_NOTES,
        {
          roadmapId,
          phaseNumber,
          taskId,
          notes,
        },
        docId
      );

      return await this.save("tasks", doc);
    } catch (error) {
      console.error("Error saving task notes:", error);
      return { success: false, error: error.message };
    }
  }

  async loadTaskNotes(roadmapId, phaseNumber, taskId) {
    try {
      const docId = `task-notes-${roadmapId}-${phaseNumber}-${taskId}`;
      const result = await this.get("tasks", docId);

      if (result.success) {
        return { success: true, notes: result.doc.notes };
      }
      return { success: true, notes: "" }; // Return empty if not found
    } catch (error) {
      console.error("Error loading task notes:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Configuration operations
   */
  async saveThemeConfig(themePreference) {
    try {
      const doc = this.createDocument(
        DB_CONFIG.DOC_TYPES.THEME_CONFIG,
        {
          themePreference,
        },
        "theme-config"
      );

      return await this.save("config", doc);
    } catch (error) {
      console.error("Error saving theme config:", error);
      return { success: false, error: error.message };
    }
  }

  async loadThemeConfig() {
    try {
      const result = await this.get("config", "theme-config");

      if (result.success) {
        return { success: true, themePreference: result.doc.themePreference };
      }
      return { success: true, themePreference: "system" }; // Default
    } catch (error) {
      console.error("Error loading theme config:", error);
      return { success: false, error: error.message };
    }
  }

  async saveAppConfig(config) {
    try {
      const doc = this.createDocument(
        DB_CONFIG.DOC_TYPES.APP_CONFIG,
        {
          config,
        },
        "app-config"
      );

      return await this.save("config", doc);
    } catch (error) {
      console.error("Error saving app config:", error);
      return { success: false, error: error.message };
    }
  }

  async loadAppConfig() {
    try {
      const result = await this.get("config", "app-config");

      if (result.success) {
        return { success: true, config: result.doc.config };
      }
      return { success: true, config: null }; // Return null if not found
    } catch (error) {
      console.error("Error loading app config:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cleanup and destroy
   */
  async destroy() {
    // Cancel all sync handlers
    Object.values(this.syncHandlers).forEach((handler) => {
      if (handler && handler.cancel) {
        handler.cancel();
      }
    });

    // Close all databases
    const closePromises = Object.values(this.databases).map((db) => {
      return db.close().catch((error) => {
        console.warn("Error closing database:", error);
      });
    });

    await Promise.all(closePromises);

    this.databases = {};
    this.syncHandlers = {};
    this.isInitialized = false;
    this.eventListeners.clear();
  }
}

// Export singleton instance
export default new PouchDBService();
export { DB_CONFIG };
