/**
 * PouchDB Roadmap Persistence
 * Drop-in replacement for RoadmapPersistence.js using PouchDB
 * Maintains the same API for seamless integration
 */

import PouchDBService from './PouchDBService';

/**
 * PouchDB-based Roadmap Persistence class
 * Provides the same interface as the original RoadmapPersistence
 */
class PouchDBRoadmapPersistence {
  static STORAGE_KEYS = {
    ROADMAPS: "roadmap-visualizer-roadmaps",
    CURRENT_ROADMAP: "roadmap-visualizer-current",
    ROADMAP_METADATA: "roadmap-visualizer-metadata",
    APP_STATE: "roadmap-visualizer-app-state",
  };

  /**
   * Generate a unique ID for a roadmap based on title and timestamp
   */
  static generateRoadmapId(title) {
    const timestamp = Date.now();
    const cleanTitle = title.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase();
    return `${cleanTitle}-${timestamp}`;
  }

  /**
   * Save a roadmap with metadata
   */
  static async saveRoadmap(roadmapData, originalData = null) {
    try {
      const result = await PouchDBService.saveRoadmap(roadmapData, originalData);
      if (result.success) {
        return result.roadmapId;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("Error saving roadmap:", error);
      throw new Error("Failed to save roadmap to storage");
    }
  }

  /**
   * Load a roadmap by ID
   */
  static async loadRoadmap(roadmapId) {
    try {
      const result = await PouchDBService.loadRoadmap(roadmapId);
      if (result.success) {
        return result.data;
      } else {
        return null;
      }
    } catch (error) {
      console.error("Error loading roadmap:", error);
      return null;
    }
  }

  /**
   * Update roadmap data
   */
  static async updateRoadmapData(roadmapId, updatedData) {
    try {
      const result = await PouchDBService.updateRoadmapData(roadmapId, updatedData);
      return result.success;
    } catch (error) {
      console.error("Error updating roadmap data:", error);
      return false;
    }
  }

  /**
   * Get all roadmap metadata
   */
  static async getAllRoadmapMetadata() {
    try {
      const result = await PouchDBService.getAllRoadmapMetadata();
      if (result.success) {
        // Convert PouchDB documents to the expected format
        return result.metadata.map(doc => ({
          id: doc.roadmapId,
          title: doc.title,
          description: doc.description,
          project_level: doc.project_level,
          tags: doc.tags,
          uploadDate: doc.uploadDate,
          lastAccessed: doc.lastAccessed,
          totalPhases: doc.totalPhases,
          totalTasks: doc.totalTasks,
          progressPercentage: doc.progressPercentage || 0
        }));
      } else {
        return [];
      }
    } catch (error) {
      console.error("Error getting roadmap metadata:", error);
      return [];
    }
  }

  /**
   * Update roadmap metadata
   */
  static async updateRoadmapMetadata(metadata) {
    try {
      const result = await PouchDBService.updateRoadmapMetadata(metadata);
      return result.success;
    } catch (error) {
      console.error("Error updating roadmap metadata:", error);
      return false;
    }
  }

  /**
   * Update last accessed time for a roadmap
   */
  static async updateLastAccessed(roadmapId) {
    try {
      const result = await PouchDBService.updateLastAccessed(roadmapId);
      return result.success;
    } catch (error) {
      console.error("Error updating last accessed:", error);
      return false;
    }
  }

  /**
   * Update roadmap progress
   */
  static async updateRoadmapProgress(roadmapId, progressPercentage) {
    try {
      const result = await PouchDBService.updateRoadmapMetadata({
        roadmapId,
        progressPercentage
      });
      return result.success;
    } catch (error) {
      console.error("Error updating roadmap progress:", error);
      return false;
    }
  }

  /**
   * Delete a roadmap
   */
  static async deleteRoadmap(roadmapId) {
    try {
      const result = await PouchDBService.deleteRoadmap(roadmapId);
      return result.success;
    } catch (error) {
      console.error("Error deleting roadmap:", error);
      return false;
    }
  }

  /**
   * Set current active roadmap
   */
  static async setCurrentRoadmap(roadmapId) {
    try {
      const result = await PouchDBService.setCurrentRoadmap(roadmapId);
      return result.success;
    } catch (error) {
      console.error("Error setting current roadmap:", error);
      return false;
    }
  }

  /**
   * Get current active roadmap ID
   */
  static async getCurrentRoadmap() {
    try {
      const result = await PouchDBService.getCurrentRoadmap();
      return result.success ? result.roadmapId : null;
    } catch (error) {
      console.error("Error getting current roadmap:", error);
      return null;
    }
  }

  /**
   * Clear current roadmap
   */
  static async clearCurrentRoadmap() {
    try {
      const result = await PouchDBService.clearCurrentRoadmap();
      return result.success;
    } catch (error) {
      console.error("Error clearing current roadmap:", error);
      return false;
    }
  }

  /**
   * Save application state
   */
  static async saveAppState(state) {
    try {
      const result = await PouchDBService.saveAppConfig(state);
      return result.success;
    } catch (error) {
      console.error("Error saving app state:", error);
      return false;
    }
  }

  /**
   * Load application state
   */
  static async loadAppState() {
    try {
      const result = await PouchDBService.loadAppConfig();
      return result.success ? result.config : null;
    } catch (error) {
      console.error("Error loading app state:", error);
      return null;
    }
  }

  /**
   * Calculate total phases in roadmap data
   */
  static calculateTotalPhases(roadmapData) {
    return PouchDBService.calculateTotalPhases(roadmapData);
  }

  /**
   * Calculate total tasks in roadmap data
   */
  static calculateTotalTasks(roadmapData) {
    return PouchDBService.calculateTotalTasks(roadmapData);
  }

  /**
   * Clear all stored data (for reset functionality)
   */
  static async clearAllData() {
    try {
      // Get all roadmap metadata first
      const allMetadata = await this.getAllRoadmapMetadata();

      // Delete all roadmaps
      const deletePromises = allMetadata.map(metadata => 
        this.deleteRoadmap(metadata.id)
      );

      await Promise.all(deletePromises);

      // Clear current roadmap
      await this.clearCurrentRoadmap();

      // Clear app state
      await PouchDBService.saveAppConfig(null);

      return true;
    } catch (error) {
      console.error("Error clearing all data:", error);
      return false;
    }
  }

  /**
   * Export all data for backup
   */
  static async exportAllData() {
    try {
      const allMetadata = await this.getAllRoadmapMetadata();
      const exportData = {
        metadata: allMetadata,
        roadmaps: {},
        appState: await this.loadAppState(),
        exportDate: new Date().toISOString()
      };

      // Load all roadmap data
      for (const metadata of allMetadata) {
        const roadmapData = await this.loadRoadmap(metadata.id);
        if (roadmapData) {
          exportData.roadmaps[metadata.id] = roadmapData;
        }
      }

      return exportData;
    } catch (error) {
      console.error("Error exporting data:", error);
      return null;
    }
  }

  /**
   * Import data from backup
   */
  static async importAllData(importData) {
    try {
      if (!importData || !importData.roadmaps) {
        throw new Error("Invalid import data format");
      }

      const results = {
        imported: 0,
        errors: []
      };

      // Import roadmaps
      for (const [oldId, roadmapData] of Object.entries(importData.roadmaps)) {
        try {
          if (roadmapData && roadmapData.data) {
            await this.saveRoadmap(roadmapData.data, roadmapData.originalData);
            results.imported++;
          }
        } catch (error) {
          results.errors.push(`Failed to import roadmap ${oldId}: ${error.message}`);
        }
      }

      // Import app state
      if (importData.appState) {
        await this.saveAppState(importData.appState);
      }

      return results;
    } catch (error) {
      console.error("Error importing data:", error);
      return {
        imported: 0,
        errors: [error.message]
      };
    }
  }

  /**
   * Get database statistics
   */
  static async getStorageStats() {
    try {
      const allMetadata = await this.getAllRoadmapMetadata();
      
      return {
        totalRoadmaps: allMetadata.length,
        totalPhases: allMetadata.reduce((sum, meta) => sum + (meta.totalPhases || 0), 0),
        totalTasks: allMetadata.reduce((sum, meta) => sum + (meta.totalTasks || 0), 0),
        lastActivity: allMetadata.length > 0 
          ? Math.max(...allMetadata.map(meta => new Date(meta.lastAccessed).getTime()))
          : null
      };
    } catch (error) {
      console.error("Error getting storage stats:", error);
      return {
        totalRoadmaps: 0,
        totalPhases: 0,
        totalTasks: 0,
        lastActivity: null
      };
    }
  }
}

export default PouchDBRoadmapPersistence;
